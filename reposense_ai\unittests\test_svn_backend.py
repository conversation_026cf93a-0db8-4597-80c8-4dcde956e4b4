"""
Unit tests for the SVN backend functionality.

This module tests SVN repository integration including
connection testing, authentication, and branch discovery.
"""

import subprocess
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config, skip_if_no_network

# Import the module under test
try:
    from models import RepositoryConfig
    from repository_backends import get_backend_manager
    from repository_backends.svn_backend import SVNBackend
except ImportError:
    get_backend_manager = None  # type: ignore
    SVNBackend = None  # type: ignore
    RepositoryConfig = None  # type: ignore


@pytest.mark.unit
class TestSVNBackend:
    """Test cases for SVN backend functionality."""

    def test_svn_backend_import(self):
        """Test that SVN backend can be imported successfully."""
        assert get_backend_manager is not None, (
            "get_backend_manager should be importable"
        )
        assert RepositoryConfig is not None, "RepositoryConfig should be importable"

    def test_repository_config_creation(self):
        """Test RepositoryConfig creation for SVN repositories."""
        if RepositoryConfig is None:
            pytest.skip("RepositoryConfig not available")

        repo_config = RepositoryConfig(
            id="test-svn-repo",
            name="Test SVN Repository",
            url="http://example.com/svn/test",
            type="svn",
            username="testuser",
            password="testpass",
            enabled=True,
            monitor_all_branches=True,
        )

        assert repo_config.name == "Test SVN Repository"
        assert repo_config.type == "svn"
        assert repo_config.username == "testuser"
        assert repo_config.monitor_all_branches is True

    def test_backend_manager_initialization(self, mock_config):
        """Test backend manager initialization."""
        if get_backend_manager is None:
            pytest.skip("get_backend_manager not available")

        backend_manager = get_backend_manager()
        assert backend_manager is not None
        assert hasattr(backend_manager, "get_backend_for_repository")

    def test_svn_backend_selection(self, mock_config):
        """Test SVN backend selection for SVN repositories."""
        if get_backend_manager is None or RepositoryConfig is None:
            pytest.skip("Required modules not available")

        backend_manager = get_backend_manager()

        # Create SVN repository config
        svn_repo = RepositoryConfig(
            id="test-svn",
            name="Test SVN",
            url="http://example.com/svn/test",
            type="svn",
            enabled=True,
        )

        # Get backend for SVN repository
        backend = backend_manager.get_backend_for_repository(svn_repo, mock_config)

        if backend is not None:
            # Should be SVN backend or compatible
            assert hasattr(backend, "get_latest_revision")
            assert hasattr(backend, "get_commit_info")

    @patch("subprocess.run")
    def test_svn_info_command(self, mock_subprocess, mock_config):
        """Test SVN info command execution."""
        if SVNBackend is None or RepositoryConfig is None:
            pytest.skip("SVNBackend not available")

        # Mock successful SVN info response
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = """<?xml version="1.0" encoding="UTF-8"?>
<info>
<entry path="." revision="123" kind="dir">
<url>http://example.com/svn/test</url>
<relative-url>^/</relative-url>
<repository>
<root>http://example.com/svn/test</root>
<uuid>12345678-1234-1234-1234-123456789abc</uuid>
</repository>
<commit revision="123">
<author>testauthor</author>
<date>2025-01-01T12:00:00.000000Z</date>
</commit>
</entry>
</info>"""
        mock_subprocess.return_value = mock_result

        # Create SVN repository config
        repo_config = RepositoryConfig(
            id="test-svn",
            name="Test SVN",
            url="http://example.com/svn/test",
            type="svn",
            enabled=True,
        )

        # Test SVN backend functionality
        if SVNBackend is not None:
            backend = SVNBackend(mock_config)

            # Test getting latest revision
            if hasattr(backend, "get_latest_revision"):
                revision = backend.get_latest_revision(repo_config)

                if revision is not None:
                    assert revision == "123"

    @patch("subprocess.run")
    def test_svn_authentication(self, mock_subprocess, mock_config):
        """Test SVN authentication handling."""
        if SVNBackend is None or RepositoryConfig is None:
            pytest.skip("SVNBackend not available")

        # Mock successful authenticated SVN response
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "Revision: 456"
        mock_subprocess.return_value = mock_result

        # Create SVN repository config with credentials
        repo_config = RepositoryConfig(
            id="test-svn-auth",
            name="Test SVN Auth",
            url="http://example.com/svn/private",
            type="svn",
            username="authuser",
            password="authpass",
            enabled=True,
        )

        if SVNBackend is not None:
            backend = SVNBackend(mock_config)

            # Test authenticated access
            if hasattr(backend, "get_latest_revision"):
                revision = backend.get_latest_revision(repo_config)

                # Verify authentication parameters were used
                if mock_subprocess.called:
                    call_args = mock_subprocess.call_args[0][0]
                    assert "--username" in call_args
                    assert "authuser" in call_args
                    assert "--password" in call_args

    @patch("subprocess.run")
    def test_svn_error_handling(self, mock_subprocess, mock_config):
        """Test SVN error handling."""
        if SVNBackend is None or RepositoryConfig is None:
            pytest.skip("SVNBackend not available")

        # Mock SVN command failure
        mock_result = MagicMock()
        mock_result.returncode = 1
        mock_result.stderr = "svn: E170001: Authentication failed"
        mock_subprocess.return_value = mock_result

        # Create SVN repository config
        repo_config = RepositoryConfig(
            id="test-svn-error",
            name="Test SVN Error",
            url="http://example.com/svn/private",
            type="svn",
            enabled=True,
        )

        if SVNBackend is not None:
            backend = SVNBackend(mock_config)

            # Test error handling
            if hasattr(backend, "get_latest_revision"):
                revision = backend.get_latest_revision(repo_config)

                # Should handle error gracefully
                assert revision is None or revision == ""

    def test_svn_url_validation(self):
        """Test SVN URL validation logic."""
        if RepositoryConfig is None:
            pytest.skip("RepositoryConfig not available")

        # Test valid SVN URLs
        valid_urls = [
            "http://example.com/svn/repo",
            "https://secure.example.com/svn/repo",
            "svn://svn.example.com/repo",
            "svn+ssh://<EMAIL>/repo",
        ]

        for url in valid_urls:
            repo_config = RepositoryConfig(
                id=f"test-{hash(url)}",
                name="Test Repository",
                url=url,
                type="svn",
                enabled=True,
            )

            assert repo_config.url == url
            assert repo_config.type == "svn"

    @patch("subprocess.run")
    def test_branch_discovery(self, mock_subprocess, mock_config):
        """Test SVN branch discovery functionality."""
        if SVNBackend is None or RepositoryConfig is None:
            pytest.skip("SVNBackend not available")

        # Mock SVN list response with proper XML format
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = """<?xml version="1.0" encoding="UTF-8"?>
<lists>
<list path="http://example.com/svn/repo">
<entry kind="dir">
<name>trunk</name>
<commit revision="100">
<author>testauthor</author>
<date>2025-01-01T12:00:00.000000Z</date>
</commit>
</entry>
<entry kind="dir">
<name>branches</name>
<commit revision="99">
<author>testauthor</author>
<date>2025-01-01T11:00:00.000000Z</date>
</commit>
</entry>
<entry kind="dir">
<name>tags</name>
<commit revision="98">
<author>testauthor</author>
<date>2025-01-01T10:00:00.000000Z</date>
</commit>
</entry>
</list>
</lists>"""
        mock_subprocess.return_value = mock_result

        # Create SVN repository config
        repo_config = RepositoryConfig(
            id="test-svn-branches",
            name="Test SVN Branches",
            url="http://example.com/svn/repo",
            type="svn",
            monitor_all_branches=True,
            enabled=True,
        )

        if SVNBackend is not None:
            backend = SVNBackend(mock_config)

            # Test branch discovery - check if method exists and handle gracefully
            if hasattr(backend, "_discover_svn_branches"):
                try:
                    branches = backend._discover_svn_branches(
                        repo_config.url,
                        repo_config.name,
                        repo_config.username,
                        repo_config.password,
                    )

                    if branches is not None and len(branches) > 0:
                        # Should discover trunk and branches
                        branch_names = [b.name for b in branches]
                        assert "trunk" in branch_names or any(
                            "trunk" in name for name in branch_names
                        )
                    else:
                        # If no branches discovered, that's acceptable for this test
                        # The important thing is that the method doesn't crash
                        assert branches is not None or branches == []

                except Exception as e:
                    # If the method doesn't exist or has different signature, skip gracefully
                    pytest.skip(
                        f"SVN branch discovery method not available or incompatible: {e}"
                    )
            else:
                # Method doesn't exist, which is acceptable
                pytest.skip("SVN branch discovery method not implemented")


@pytest.mark.integration
@pytest.mark.network
class TestSVNBackendIntegration:
    """Integration tests for SVN backend with real repositories."""

    @pytest.mark.skipif(skip_if_no_network(), reason="Network not available")
    def test_public_svn_repository(self, mock_config):
        """Test connection to a public SVN repository."""
        if get_backend_manager is None or RepositoryConfig is None:
            pytest.skip("Required modules not available")

        # Use a known public SVN repository for testing
        public_repo = RepositoryConfig(
            id="test-public-svn",
            name="Public SVN Test",
            url="http://svn.apache.org/repos/asf/subversion/trunk",
            type="svn",
            enabled=True,
        )

        backend_manager = get_backend_manager()
        backend = backend_manager.get_backend_for_repository(public_repo, mock_config)

        if backend is not None:
            # Test basic connectivity
            try:
                revision = backend.get_latest_revision(public_repo)
                # If successful, revision should be a string or number
                if revision is not None:
                    assert isinstance(revision, (str, int))
                    assert str(revision).isdigit()
            except Exception:
                # Network issues are acceptable in integration tests
                pytest.skip("Public SVN repository not accessible")

    def test_svn_command_availability(self):
        """Test that SVN command-line tools are available."""
        try:
            result = subprocess.run(
                ["svn", "--version"], capture_output=True, text=True, timeout=5
            )

            if result.returncode == 0:
                assert "svn, version" in result.stdout
            else:
                pytest.skip("SVN command-line tools not available")

        except (subprocess.TimeoutExpired, FileNotFoundError):
            pytest.skip("SVN command-line tools not available")


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
