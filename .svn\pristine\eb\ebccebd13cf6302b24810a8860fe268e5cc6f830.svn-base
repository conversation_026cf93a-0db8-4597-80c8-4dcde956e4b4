{"python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["reposense_ai/unittests"], "python.testing.cwd": "${workspaceFolder}", "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=100"], "files.associations": {"*.py": "python"}, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.autoSearchPaths": true, "python.analysis.extraPaths": ["./reposense_ai"], "editor.rulers": [100], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.git": true, "**/.svn": true, "**/data": true, "**/logs": true}, "search.exclude": {"**/node_modules": true, "**/data": true, "**/logs": true, "**/.git": true, "**/.svn": true, "**/__pycache__": true, "**/.pytest_cache": true}}