# ===================================================================
# RepoSense AI - Consolidated Requirements
# ===================================================================
# This file contains all Python dependencies for RepoSense AI
# Consolidates both core and document processing requirements

# === CORE WEB FRAMEWORK ===
requests>=2.31.0           # HTTP client for Ollama API calls
flask>=2.3.0               # Web framework
werkzeug>=2.3.0            # WSGI utilities
jinja2>=3.1.0              # Template engine
markupsafe>=2.1.0          # Template security

# === PDF GENERATION ===
reportlab>=4.0.0           # PDF generation for exports

# === DOCUMENT PROCESSING - CORE ===
# Basic document support (always installed)
python-docx>=0.8.11        # Word documents (.docx)
docx2txt>=0.8              # Lightweight .docx text extraction
striprtf>=0.0.26           # Rich Text Format (.rtf)
odfpy>=1.4.1               # OpenDocument formats (.odt, .ods, .odp)

# === DOCUMENT PROCESSING - ENHANCED ===
# Microsoft Office support
openpyxl>=3.1.0            # Excel spreadsheets (.xlsx)
xlrd>=2.0.1                # Legacy Excel files (.xls)
python-pptx>=0.6.21        # PowerPoint presentations (.pptx)

# Advanced PDF processing
pdfplumber>=0.7.6          # Advanced PDF text extraction
PyPDF2>=3.0.1              # Basic PDF text extraction (fallback)

# Text processing and encoding
chardet>=3.0.4             # Character encoding detection (compatible version)

# === OPTIONAL DOCUMENT PROCESSING ===
# Note: textract has strict dependency conflicts with newer packages
# Uncomment if needed, but may require downgrading other packages:
# textract==1.6.3          # Universal document extractor (has strict version requirements)

# === BUILD DEPENDENCIES ===
pyinstaller>=6.0.0         # Binary creation

# === TESTING DEPENDENCIES ===
pytest>=7.4.0              # Testing framework
pytest-cov>=4.1.0          # Coverage reporting
pytest-mock>=3.11.0        # Mocking utilities
pytest-timeout>=2.1.0      # Test timeout handling
pytest-xdist>=3.3.0        # Parallel test execution

# === OPTIONAL DEPENDENCIES ===
# Uncomment if needed for specific use cases:
# pandas>=1.5.0            # Advanced spreadsheet analysis
# pymupdf>=1.23.0          # High-performance PDF processing (requires system libs)
# patool>=1.12             # Universal archive extractor
# python-magic>=0.4.27     # File type detection (requires libmagic)

# ===================================================================
# INSTALLATION NOTES
# ===================================================================
#
# System dependencies for textract (already handled in Dockerfile):
# - Linux: antiword unrtf poppler-utils pstotext tesseract-ocr
# - macOS: brew install antiword unrtf poppler tesseract
# - Windows: Use WSL or install individual tools
#
# For python-magic (if uncommented):
# - Linux: sudo apt-get install libmagic1
# - macOS: brew install libmagic
# - Windows: pip install python-magic-bin
#
# ===================================================================
