"""
RepoSense AI Unit Tests Package

This package contains comprehensive unit tests for the RepoSense AI application.
Tests are organized by component and can be run individually or as a complete suite.

Test Categories:
- unit: Individual component tests
- integration: Component interaction tests
- database: Database-related tests
- network: Network/API tests
- ai: AI/LLM service tests

Usage:
    # Run all tests
    pytest reposense_ai/unittests/

    # Run specific test categories
    pytest reposense_ai/unittests/ -m unit
    pytest reposense_ai/unittests/ -m integration

    # Run specific test file
    pytest reposense_ai/unittests/test_config_manager.py

    # Run with coverage
    pytest reposense_ai/unittests/ --cov=reposense_ai --cov-report=html
"""

import sys
import os
from pathlib import Path

# Add the parent directory to Python path for imports
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

__version__ = "1.0.0"
__author__ = "RepoSense AI Team"
