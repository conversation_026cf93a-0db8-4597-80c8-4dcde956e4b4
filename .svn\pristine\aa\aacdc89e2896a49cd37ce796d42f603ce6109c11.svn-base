"""
Unit tests for the OllamaClient class.

This module tests the Ollama AI service integration including
connection testing, model availability, and content generation.
"""

from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
import requests
from requests.exceptions import ConnectionError, RequestException, Timeout

# Import test utilities
from .test_utils import MockResponse, mock_config, skip_if_no_ai_service

# Import the module under test
try:
    from ollama_client import OllamaClient
except ImportError:
    OllamaClient = None  # type: ignore


@pytest.mark.unit
class TestOllamaClient:
    """Test cases for OllamaClient class."""

    def test_ollama_client_import(self):
        """Test that OllamaClient can be imported successfully."""
        assert OllamaClient is not None, "OllamaClient should be importable"

    def test_ollama_client_initialization(self, mock_config):
        """Test OllamaClient initialization with config."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        client = OllamaClient(mock_config)
        assert client is not None
        assert client.config == mock_config
        assert hasattr(client, "test_connection")
        assert hasattr(client, "call_ollama")

    @patch("requests.get")
    def test_direct_connection_success(self, mock_get, mock_config):
        """Test successful direct connection to Ollama API."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Mock successful /api/tags response
        mock_response = MockResponse(
            {
                "models": [
                    {"name": "smollm2:latest"},
                    {"name": "qwen3-coder:latest"},
                    {"name": "granite3.3:8b"},
                ]
            },
            200,
        )
        mock_get.return_value = mock_response

        client = OllamaClient(mock_config)

        # Test connection
        result = client.test_connection()
        assert result is True

        # Verify the request was made correctly
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "/api/tags" in call_args[0][0]

    @patch("requests.get")
    def test_direct_connection_failure(self, mock_get, mock_config):
        """Test failed direct connection to Ollama API."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Mock connection failure
        mock_get.side_effect = ConnectionError("Connection refused")

        client = OllamaClient(mock_config)

        # Test connection failure
        result = client.test_connection()
        assert result is False

    @patch("requests.get")
    def test_get_available_models_success(self, mock_get, mock_config):
        """Test successful model listing."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        expected_models = ["smollm2:latest", "qwen3-coder:latest", "granite3.3:8b"]
        mock_response = MockResponse(
            {"models": [{"name": model} for model in expected_models]}, 200
        )
        mock_get.return_value = mock_response

        client = OllamaClient(mock_config)

        # Test model listing
        if hasattr(client, "get_available_models"):
            models = client.get_available_models()
            assert models == expected_models

    @patch("requests.get")
    @patch("requests.post")
    def test_content_generation_success(self, mock_post, mock_get, mock_config):
        """Test successful content generation."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Mock model availability check
        mock_get.return_value = MockResponse(
            {"models": [{"name": "smollm2:latest"}]}, 200
        )

        # Mock successful generation response
        mock_response = MockResponse(
            {"response": "Test successful - AI response generated correctly"}, 200
        )
        mock_post.return_value = mock_response

        # Configure mock with proper model name
        mock_config.ollama_model_documentation = "smollm2:latest"

        client = OllamaClient(mock_config)

        # Test content generation
        result = client.call_ollama("Hello, this is a test.")
        assert result is not None
        assert "Test successful" in result

        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert "/api/generate" in call_args[0][0]

        # Check request payload
        request_data = call_args[1]["json"]
        assert "model" in request_data
        assert "prompt" in request_data
        assert request_data["stream"] is False

    @patch("requests.get")
    @patch("requests.post")
    def test_content_generation_timeout(self, mock_post, mock_get, mock_config):
        """Test content generation timeout handling."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Mock model availability check
        mock_get.return_value = MockResponse(
            {"models": [{"name": "smollm2:latest"}]}, 200
        )

        # Mock timeout
        mock_post.side_effect = Timeout("Request timed out")

        # Configure mock with proper model name
        mock_config.ollama_model_documentation = "smollm2:latest"

        client = OllamaClient(mock_config)

        # Test timeout handling
        result = client.call_ollama("Hello, this is a test.")
        assert result is None or result == "" or "Error:" in result

    @patch("requests.get")
    @patch("requests.post")
    def test_content_generation_error_response(self, mock_post, mock_get, mock_config):
        """Test content generation with error response."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Mock model availability check
        mock_get.return_value = MockResponse(
            {"models": [{"name": "smollm2:latest"}]}, 200
        )

        # Mock error response
        mock_response = MockResponse({"error": "Model not found"}, 404)
        mock_post.return_value = mock_response

        # Configure mock with proper model name
        mock_config.ollama_model_documentation = "smollm2:latest"

        client = OllamaClient(mock_config)

        # Test error handling
        result = client.call_ollama("Hello, this is a test.")
        assert result is None or result == "" or "Error:" in result

    def test_model_selection_logic(self, mock_config):
        """Test model selection logic for different purposes."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        client = OllamaClient(mock_config)

        # Test that client uses the correct model from config
        assert client.config.ollama_model_documentation == "smollm2:latest"
        assert client.config.ollama_model_code_review == "qwen3-coder:latest"
        assert client.config.ollama_model_risk_assessment == "granite3.3:8b"

    @patch("requests.get")
    @patch("requests.post")
    def test_full_workflow(self, mock_post, mock_get, mock_config):
        """Test complete Ollama workflow: connection -> model check -> generation."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Configure mock with proper model name
        mock_config.ollama_model_documentation = "smollm2:latest"

        # Mock successful connection
        mock_get.return_value = MockResponse(
            {"models": [{"name": "smollm2:latest"}]}, 200
        )

        # Mock successful generation
        mock_post.return_value = MockResponse(
            {"response": "Workflow test successful"}, 200
        )

        client = OllamaClient(mock_config)

        # Test full workflow
        connection_ok = client.test_connection()
        assert connection_ok is True

        result = client.call_ollama("Test workflow")
        assert result is not None
        assert "successful" in result or "Workflow test successful" in result


@pytest.mark.integration
@pytest.mark.ai
class TestOllamaClientIntegration:
    """Integration tests for OllamaClient with real AI service."""

    @pytest.mark.skipif(skip_if_no_ai_service(), reason="AI service not available")
    def test_real_connection(self, mock_config):
        """Test connection to real Ollama service if available."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Update config to use real service
        mock_config.ollama_host = "http://localhost:11434"

        client = OllamaClient(mock_config)

        # Test real connection (will be skipped if service not available)
        result = client.test_connection()
        assert isinstance(result, bool)

    @pytest.mark.skipif(skip_if_no_ai_service(), reason="AI service not available")
    def test_real_generation(self, mock_config):
        """Test content generation with real Ollama service if available."""
        if OllamaClient is None:
            pytest.skip("OllamaClient not available")

        # Update config to use real service
        mock_config.ollama_host = "http://localhost:11434"

        client = OllamaClient(mock_config)

        # Test real generation (will be skipped if service not available)
        result = client.call_ollama(
            "Hello, please respond with 'Integration test successful'."
        )

        if result is not None:
            assert isinstance(result, str)
            assert len(result) > 0


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
