#!/usr/bin/env python3
"""
Test runner script for RepoSense AI unit tests.

This script provides a convenient way to run tests with various options
and configurations from the command line or IDE.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def setup_python_path():
    """Ensure the current directory is in Python path for imports."""
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))


def run_pytest(args):
    """Run pytest with the specified arguments."""
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test directory
    cmd.append("unittests/")
    
    # Add additional arguments
    cmd.extend(args)
    
    print(f"Running: {' '.join(cmd)}")
    print("-" * 50)
    
    # Run the command
    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="RepoSense AI Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --unit             # Run only unit tests
  python run_tests.py --verbose          # Run with verbose output
  python run_tests.py --coverage         # Run with coverage report
  python run_tests.py --file config      # Run config manager tests
  python run_tests.py --fast             # Skip slow tests
        """
    )
    
    # Test selection options
    parser.add_argument(
        "--unit", 
        action="store_true", 
        help="Run only unit tests"
    )
    parser.add_argument(
        "--integration", 
        action="store_true", 
        help="Run only integration tests"
    )
    parser.add_argument(
        "--database", 
        action="store_true", 
        help="Run only database tests"
    )
    parser.add_argument(
        "--network", 
        action="store_true", 
        help="Run only network tests"
    )
    parser.add_argument(
        "--ai", 
        action="store_true", 
        help="Run only AI service tests"
    )
    parser.add_argument(
        "--fast", 
        action="store_true", 
        help="Skip slow tests"
    )
    parser.add_argument(
        "--file", 
        type=str, 
        help="Run tests for specific file (e.g., 'config' for test_config_manager.py)"
    )
    
    # Output options
    parser.add_argument(
        "--verbose", "-v", 
        action="store_true", 
        help="Verbose output"
    )
    parser.add_argument(
        "--quiet", "-q", 
        action="store_true", 
        help="Quiet output"
    )
    parser.add_argument(
        "--coverage", 
        action="store_true", 
        help="Run with coverage report"
    )
    parser.add_argument(
        "--html-coverage", 
        action="store_true", 
        help="Generate HTML coverage report"
    )
    
    # Debugging options
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug output"
    )
    parser.add_argument(
        "--pdb", 
        action="store_true", 
        help="Drop into debugger on failures"
    )
    parser.add_argument(
        "--lf", 
        action="store_true", 
        help="Run last failed tests only"
    )
    
    # Performance options
    parser.add_argument(
        "--parallel", "-n", 
        type=int, 
        help="Run tests in parallel (number of workers)"
    )
    parser.add_argument(
        "--durations", 
        type=int, 
        default=10, 
        help="Show slowest N test durations"
    )
    
    args = parser.parse_args()
    
    # Setup Python path
    setup_python_path()
    
    # Build pytest arguments
    pytest_args = []
    
    # Test selection
    if args.unit:
        pytest_args.extend(["-m", "unit"])
    elif args.integration:
        pytest_args.extend(["-m", "integration"])
    elif args.database:
        pytest_args.extend(["-m", "database"])
    elif args.network:
        pytest_args.extend(["-m", "network"])
    elif args.ai:
        pytest_args.extend(["-m", "ai"])
    elif args.fast:
        pytest_args.extend(["-m", "not slow"])
    
    # File selection
    if args.file:
        pytest_args.append(f"unittests/test_{args.file}.py")
        # Remove the default unittests/ directory since we're specifying a file
        if "unittests/" in pytest_args:
            pytest_args.remove("unittests/")
    
    # Output options
    if args.verbose:
        pytest_args.append("-v")
    elif args.quiet:
        pytest_args.append("-q")
    
    # Coverage options
    if args.coverage or args.html_coverage:
        pytest_args.extend(["--cov=.", "--cov-report=term-missing"])
        if args.html_coverage:
            pytest_args.append("--cov-report=html")
    
    # Debugging options
    if args.debug:
        pytest_args.extend(["-s", "--tb=long"])
    if args.pdb:
        pytest_args.append("--pdb")
    if args.lf:
        pytest_args.append("--lf")
    
    # Performance options
    if args.parallel:
        pytest_args.extend(["-n", str(args.parallel)])
    
    pytest_args.extend(["--durations", str(args.durations)])
    
    # Default options if none specified
    if not any([args.unit, args.integration, args.database, args.network, args.ai, args.fast, args.file]):
        # Add some default options for better output
        if not args.quiet:
            pytest_args.append("-v")
        pytest_args.extend(["--tb=short", "--color=yes"])
    
    # Run the tests
    return run_pytest(pytest_args)


if __name__ == "__main__":
    sys.exit(main())
