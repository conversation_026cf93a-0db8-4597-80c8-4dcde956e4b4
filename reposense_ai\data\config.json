{"server": {"id": "f90fb8df-a466-4328-ad3f-966a16b9444d", "name": "home", "description": "Fred svn server", "base_url": "http://sundc:81/svn", "default_username": "fvaneijk", "default_password": "an<PERSON><PERSON><PERSON>", "enabled": true}, "users": [{"id": "1b41bbc5-577f-4933-83df-233d8deb0c29", "username": "admin", "email": "<EMAIL>", "full_name": "System Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-20T11:58:34.888793", "last_modified": "2025-08-20T11:58:34.888808"}, {"id": "8fb08760-9864-4248-ac96-3668385b27a6", "username": "manager", "email": "<EMAIL>", "full_name": "Project Manager", "role": "manager", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-20T11:58:34.890440", "last_modified": "2025-08-20T11:58:34.890446"}], "repositories": [{"id": "58f825dc-6832-4fd4-b2d5-b1a8efc29feb", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 8, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 8, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-20T12:21:28.408853", "total_revisions": null, "processed_revisions": 8, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/README.md"], "risk_aggressiveness": "BALANCED", "risk_description": ""}, {"id": "cc2858cd-fa8d-44be-8131-f2c4aee50103", "name": "reposense_cpp_test", "url": "http://sundc:81/svn/reposense_cpp_test", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 17, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 17, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 17, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-21T11:53:23.965976", "total_revisions": null, "processed_revisions": 17, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/docs/API_MIGRATION_GUIDE.md", "/docs/CONFIGURATION.md", "/docs/SECURITY_ADVISORY_CVE-2025-0001.md"], "risk_aggressiveness": "BALANCED", "risk_description": ""}, {"id": "21613031-75e7-4b0e-8f08-061874539383", "name": "visionApi", "url": "http://sundc:81/svn/visionApi", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 7, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 7, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 7, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-23T11:41:05.514780", "total_revisions": null, "processed_revisions": 7, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "BALANCED", "risk_description": ""}, {"id": "23577c38-686c-46bb-8fa7-82125254f71d", "name": "Proteus-VSM-Projects", "url": "http://sundc:81/svn/Proteus-VSM-Projects", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 26, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 26, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 26, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-24T23:41:57.183841", "total_revisions": null, "processed_revisions": 26, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "BALANCED", "risk_description": ""}], "ollama_host": "http://************:11434", "ollama_model": "smollm2:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "skip_initial_scan": false, "cleanup_orphaned_documents": false, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": ["<EMAIL>", "<EMAIL>"], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "75b0da6de4dc560a4594ffc806d3b677945ce1661a367d3e6a4754dabf35b32c", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5}