"""
Unit tests for the MonitorService class.

This module tests the core monitoring functionality including
repository monitoring, document processing, and workflow integration.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# Import test utilities
from .test_utils import mock_config, mock_document_record, mock_ollama_client

# Import the module under test
try:
    from monitor_service import MonitorService
    from models import RepositoryConfig, CommitInfo
except ImportError:
    MonitorService = None  # type: ignore
    RepositoryConfig = None  # type: ignore
    CommitInfo = None  # type: ignore


@pytest.mark.unit
class TestMonitorService:
    """Test cases for MonitorService class."""
    
    def test_monitor_service_import(self):
        """Test that MonitorService can be imported successfully."""
        assert MonitorService is not None, "MonitorService should be importable"
        assert RepositoryConfig is not None, "RepositoryConfig should be importable"
        assert CommitInfo is not None, "CommitInfo should be importable"
    
    def test_monitor_service_initialization(self, mock_config):
        """Test MonitorService initialization."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        service = MonitorService(mock_config)
        assert service is not None
        assert service.config == mock_config
        assert hasattr(service, 'start_monitoring')
        assert hasattr(service, 'stop_monitoring')
    
    def test_repository_config_validation(self):
        """Test repository configuration validation."""
        if RepositoryConfig is None:
            pytest.skip("RepositoryConfig not available")
        
        # Test valid repository config
        valid_repo = RepositoryConfig(
            id="test-repo-001",
            name="Test Repository",
            url="https://github.com/test/repo.git",
            type="git",
            enabled=True,
            monitor_all_branches=False
        )
        
        assert valid_repo.name == "Test Repository"
        assert valid_repo.type == "git"
        assert valid_repo.enabled is True
        
        # Test SVN repository config
        svn_repo = RepositoryConfig(
            id="test-svn-001",
            name="Test SVN Repository",
            url="http://example.com/svn/repo",
            type="svn",
            username="testuser",
            password="testpass",
            enabled=True,
            monitor_all_branches=True
        )
        
        assert svn_repo.type == "svn"
        assert svn_repo.username == "testuser"
        assert svn_repo.monitor_all_branches is True
    
    def test_commit_info_creation(self):
        """Test CommitInfo object creation."""
        if CommitInfo is None:
            pytest.skip("CommitInfo not available")
        
        commit = CommitInfo(
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="abc123def456",
            author="Test Author <<EMAIL>>",
            date=datetime.now().isoformat(),
            message="Test commit message\n\nDetailed description of changes.",
            changed_paths=["src/main.py", "tests/test_main.py", "README.md"],
            diff="@@ -1,3 +1,4 @@\n def main():\n+    print('Hello, World!')\n     pass"
        )
        
        assert commit.repository_id == "test-repo"
        assert commit.revision == "abc123def456"
        assert len(commit.changed_paths) == 3
        assert "src/main.py" in commit.changed_paths
        assert "Hello, World!" in commit.diff
    
    @patch('time.sleep')
    def test_monitoring_loop_start_stop(self, mock_sleep, mock_config):
        """Test monitoring loop start and stop functionality."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        service = MonitorService(mock_config)
        
        # Mock the monitoring loop to avoid infinite loop
        with patch.object(service, '_monitoring_loop') as mock_loop:
            # Test start monitoring
            if hasattr(service, 'start_monitoring'):
                service.start_monitoring()
                
                # Verify monitoring started
                if hasattr(service, 'is_monitoring'):
                    assert service.is_monitoring is True
            
            # Test stop monitoring
            if hasattr(service, 'stop_monitoring'):
                service.stop_monitoring()
                
                # Verify monitoring stopped
                if hasattr(service, 'is_monitoring'):
                    assert service.is_monitoring is False
    
    @patch('repository_backends.get_backend_manager')
    def test_repository_processing(self, mock_backend_manager, mock_config):
        """Test repository processing workflow."""
        if MonitorService is None or RepositoryConfig is None:
            pytest.skip("Required modules not available")
        
        # Mock backend manager and backend
        mock_backend = MagicMock()
        mock_backend.get_latest_revision.return_value = "123"
        mock_backend.get_commit_info.return_value = self._create_test_commit()
        
        mock_manager = MagicMock()
        mock_manager.get_backend_for_repository.return_value = mock_backend
        mock_backend_manager.return_value = mock_manager
        
        # Create test repository
        test_repo = RepositoryConfig(
            id="test-processing",
            name="Test Processing Repository",
            url="https://github.com/test/processing.git",
            type="git",
            enabled=True
        )
        
        service = MonitorService(mock_config)
        
        # Test repository processing
        if hasattr(service, 'process_repository'):
            result = service.process_repository(test_repo)
            
            # Should process successfully
            assert result is True or result is None  # Depends on implementation
    
    @patch('document_service.DocumentService')
    def test_document_processing_integration(self, mock_doc_service_class, mock_config):
        """Test integration with document processing service."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        # Mock document service
        mock_doc_service = MagicMock()
        mock_doc_service.process_commit.return_value = True
        mock_doc_service_class.return_value = mock_doc_service
        
        service = MonitorService(mock_config)
        test_commit = self._create_test_commit()
        
        # Test document processing integration
        if hasattr(service, 'process_commit_documents'):
            result = service.process_commit_documents(test_commit)
            
            # Should integrate with document service
            assert result is True or result is None
    
    @patch('email_service.EmailService')
    def test_email_notification_integration(self, mock_email_service_class, mock_config):
        """Test integration with email notification service."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        # Mock email service
        mock_email_service = MagicMock()
        mock_email_service.send_email.return_value = True
        mock_email_service_class.return_value = mock_email_service
        
        service = MonitorService(mock_config)
        test_commit = self._create_test_commit()
        
        # Test email notification integration
        if hasattr(service, 'send_commit_notification'):
            result = service.send_commit_notification(test_commit)
            
            # Should integrate with email service
            assert result is True or result is None
    
    def test_monitoring_configuration(self, mock_config):
        """Test monitoring configuration handling."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        # Test configuration access
        service = MonitorService(mock_config)
        
        # Should have access to configuration
        assert service.config.check_interval == 300
        assert service.config.skip_initial_scan is False
        assert service.config.cleanup_orphaned_documents is False
    
    def test_error_handling_in_monitoring(self, mock_config):
        """Test error handling during monitoring operations."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        service = MonitorService(mock_config)
        
        # Test handling of invalid repository
        invalid_repo = RepositoryConfig(
            id="invalid-repo",
            name="Invalid Repository",
            url="invalid://not-a-real-url",
            type="unknown",
            enabled=True
        )
        
        # Should handle invalid repository gracefully
        if hasattr(service, 'process_repository'):
            try:
                result = service.process_repository(invalid_repo)
                # Should return False or None for invalid repository
                assert result is False or result is None
            except Exception as e:
                # Exception handling is acceptable
                assert isinstance(e, (ValueError, ConnectionError, Exception))
    
    def _create_test_commit(self):
        """Create a test CommitInfo object."""
        if CommitInfo is None:
            return Mock()
        
        return CommitInfo(
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="test123",
            author="Test Author <<EMAIL>>",
            date=datetime.now().isoformat(),
            message="Test commit for monitoring",
            changed_paths=["src/test.py", "docs/test.md"],
            diff="@@ -0,0 +1,3 @@\n+# Test file\n+def test():\n+    return True"
        )


@pytest.mark.integration
class TestMonitorServiceIntegration:
    """Integration tests for MonitorService with real components."""
    
    def test_full_monitoring_workflow(self, mock_config):
        """Test complete monitoring workflow integration."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        # This would be a comprehensive integration test
        # that tests the full workflow from repository monitoring
        # to document processing to email notifications
        
        service = MonitorService(mock_config)
        
        # Test that service can be created and configured
        assert service is not None
        assert hasattr(service, 'config')
        
        # Additional integration tests would go here
        # when testing with real repositories and services
    
    def test_monitoring_with_real_config(self):
        """Test monitoring service with realistic configuration."""
        if MonitorService is None:
            pytest.skip("MonitorService not available")
        
        # This test would use a real configuration file
        # and test the monitoring service with actual settings
        
        # For now, just verify the service can handle
        # realistic configuration scenarios
        pass


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
