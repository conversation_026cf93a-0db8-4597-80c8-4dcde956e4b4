# Visual Studio Code Setup for RepoSense AI Testing

This guide helps you set up Visual Studio Code for running and debugging tests in the RepoSense AI project.

## 🚀 Quick Setup

### 1. Install Required Extensions

Install these VS Code extensions:

- **Python** (Microsoft) - Essential for Python development
- **Python Test Explorer** (Little Fox Team) - For test discovery and execution
- **Test Explorer UI** (<PERSON><PERSON><PERSON>) - Unified test interface

### 2. Configure Python Interpreter

1. **Open Command Palette**: `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
2. **Type**: `Python: Select Interpreter`
3. **Choose**: Your system Python installation or virtual environment

**Common Python Interpreter Paths:**
- **Windows**: `C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe`
- **Windows (Microsoft Store)**: `C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe`
- **Linux/Mac**: `/usr/bin/python3` or `/usr/local/bin/python3`
- **Virtual Environment**: `./venv/Scripts/python.exe` (Windows) or `./venv/bin/python` (Linux/Mac)

### 3. Install Testing Dependencies

Open the integrated terminal (`Ctrl+`` `) and run:

```bash
# Navigate to the reposense_ai directory
cd reposense_ai

# Install testing dependencies
pip install pytest pytest-cov pytest-mock pytest-timeout pytest-xdist
```

### 4. Verify Test Discovery

1. **Open Test Explorer**: Click the test beaker icon in the sidebar
2. **Refresh Tests**: Click the refresh button if tests don't appear automatically
3. **You should see**: `reposense_ai/unittests/test_config_manager.py` with test functions

## 🧪 Running Tests

### From Test Explorer (Recommended)

1. **Open Test Explorer** (beaker icon in sidebar)
2. **Expand test tree** to see individual tests
3. **Click play button** next to any test or test file to run
4. **View results** in the Test Results panel

### From Command Palette

1. **Open Command Palette**: `Ctrl+Shift+P`
2. **Type**: `Python: Run All Tests` or `Python: Run Current Test File`
3. **Select** the appropriate command

### From Integrated Terminal

```bash
# Run all tests
python -m pytest reposense_ai/unittests/ -v

# Run specific test file
python -m pytest reposense_ai/unittests/test_config_manager.py -v

# Run specific test function
python -m pytest reposense_ai/unittests/test_config_manager.py::test_basic_functionality -v
```

## 🐛 Debugging Tests

### Set Breakpoints

1. **Open test file** (e.g., `test_config_manager.py`)
2. **Click in the gutter** next to line numbers to set breakpoints
3. **Red dots** indicate active breakpoints

### Debug Individual Tests

1. **Right-click** on a test function in the Test Explorer
2. **Select**: "Debug Test"
3. **Execution will pause** at breakpoints
4. **Use debug controls** to step through code

### Debug from Command Palette

1. **Open Command Palette**: `Ctrl+Shift+P`
2. **Type**: `Python: Debug Current Test`
3. **Select** the command to debug the currently open test file

## ⚙️ Configuration Files

The project includes pre-configured VS Code settings:

### `.vscode/settings.json`
- Enables pytest as the test framework
- Configures test discovery paths
- Sets up Python analysis and formatting

### `.vscode/launch.json`
- Debug configurations for tests
- Environment variable setup
- Multiple launch options for different scenarios

### `pytest.ini`
- Pytest configuration
- Test markers and categories
- Logging and output settings

## 🔧 Troubleshooting

### "Test loading failed" Error

**Problem**: VS Code can't find Python interpreter
**Solution**: 
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type `Python: Select Interpreter`
3. Choose a valid Python installation

### "No tests discovered" Error

**Problem**: Tests not found in expected location
**Solutions**:
1. Ensure you're in the project root directory
2. Check that test files start with `test_`
3. Verify pytest is installed: `pip install pytest`
4. Refresh test discovery in Test Explorer

### Import Errors in Tests

**Problem**: Tests can't import project modules
**Solutions**:
1. Check `PYTHONPATH` is set correctly in launch configurations
2. Ensure you're running tests from the correct directory
3. Verify the project structure matches the import paths

### Python Extension Issues

**Problem**: Python extension not working properly
**Solutions**:
1. Reload VS Code window: `Ctrl+Shift+P` → `Developer: Reload Window`
2. Restart VS Code completely
3. Check Python extension is enabled and up to date

## 📊 Test Categories

Use these markers to run specific test categories:

```bash
# Run only unit tests
python -m pytest reposense_ai/unittests/ -m unit

# Run only integration tests
python -m pytest reposense_ai/unittests/ -m integration

# Skip slow tests
python -m pytest reposense_ai/unittests/ -m "not slow"
```

## 🎯 Next Steps

1. **Write more tests** following the examples in `test_config_manager.py`
2. **Use test utilities** from `test_utils.py` for common testing needs
3. **Add test markers** to categorize your tests appropriately
4. **Run tests regularly** during development to catch issues early

## 📚 Additional Resources

- [VS Code Python Testing](https://code.visualstudio.com/docs/python/testing)
- [Pytest Documentation](https://docs.pytest.org/)
- [Python Test Explorer Extension](https://marketplace.visualstudio.com/items?itemName=LittleFoxTeam.vscode-python-test-adapter)
