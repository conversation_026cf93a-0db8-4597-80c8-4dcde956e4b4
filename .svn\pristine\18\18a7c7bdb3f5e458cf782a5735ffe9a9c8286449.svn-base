"""
Test utilities and common fixtures for RepoSense AI unit tests.

This module provides common test utilities, fixtures, and helper functions
that can be used across multiple test files.
"""

import os
import shutil
import sys
import tempfile
from pathlib import Path
from typing import Any, Dict, Optional
from unittest.mock import MagicMock, Mock

import pytest

# Ensure the parent directory is in the Python path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))


class TestConfig:
    """Test configuration and constants."""

    # Test database settings
    TEST_DB_NAME = "test_documents.db"
    TEST_DATA_DIR = "test_data"

    # Mock AI model responses
    MOCK_AI_RESPONSE = {
        "summary": "Test commit summary",
        "risk_assessment": "LOW",
        "change_type": "FEATURE",
        "impact_analysis": "Minor changes to test functionality",
    }

    # Test repository data
    TEST_REPO_DATA = {
        "name": "test_repo",
        "url": "https://example.com/test_repo",
        "type": "git",
        "branch": "main",
    }


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_config():
    """Create a mock configuration object."""
    config = Mock()
    config.ollama_host = (
        "http://localhost:11434"  # Note: ollama_host not ollama_base_url
    )
    config.ollama_model = "smollm2:latest"  # Default model used by OllamaClient
    config.ollama_model_documentation = "smollm2:latest"
    config.ollama_model_code_review = "qwen3-coder:latest"
    config.ollama_model_risk_assessment = "granite3.3:8b"
    config.email_enabled = False
    config.database_path = TestConfig.TEST_DB_NAME
    return config


@pytest.fixture
def mock_document_record():
    """Create a mock document record for testing."""
    from document_database import DocumentRecord

    record = Mock(spec=DocumentRecord)
    record.id = 1
    record.filename = "test_document.py"
    record.filepath = "/test/path/test_document.py"
    record.repository_name = "test_repo"
    record.revision = "123"
    record.commit_message = "Test commit message"
    record.changed_paths = ["test_document.py"]
    record.ai_model = "smollm2:latest"
    record.processing_date = "2025-08-23 12:00:00"
    return record


@pytest.fixture
def mock_ollama_client():
    """Create a mock Ollama client for testing."""
    client = Mock()
    client.is_available.return_value = True
    client.get_available_models.return_value = [
        "smollm2:latest",
        "qwen3-coder:latest",
        "granite3.3:8b",
    ]
    client.generate_response.return_value = TestConfig.MOCK_AI_RESPONSE
    return client


def create_test_file(directory: Path, filename: str, content: str = "") -> Path:
    """Create a test file with specified content."""
    file_path = directory / filename
    file_path.parent.mkdir(parents=True, exist_ok=True)
    file_path.write_text(content, encoding="utf-8")
    return file_path


def create_mock_database():
    """Create a mock database for testing."""
    db = Mock()
    db.get_document_by_id.return_value = None
    db.add_document.return_value = 1
    db.update_document.return_value = True
    db.get_all_documents.return_value = []
    db.get_documents_by_repository.return_value = []
    return db


def assert_file_exists(file_path: Path, message: str = ""):
    """Assert that a file exists."""
    assert file_path.exists(), f"File should exist: {file_path}. {message}"


def assert_file_contains(file_path: Path, content: str, message: str = ""):
    """Assert that a file contains specific content."""
    assert file_path.exists(), f"File should exist: {file_path}"
    file_content = file_path.read_text(encoding="utf-8")
    assert content in file_content, f"File should contain '{content}'. {message}"


def assert_valid_json_response(response_data: Dict[str, Any]):
    """Assert that a response is valid JSON with expected structure."""
    assert isinstance(response_data, dict), "Response should be a dictionary"
    assert "status" in response_data, "Response should have 'status' field"


def skip_if_no_network():
    """Skip test if network is not available."""
    try:
        import socket

        socket.create_connection(("*******", 53), timeout=3)
        return False
    except OSError:
        return True


def skip_if_no_ai_service():
    """Skip test if AI service is not available."""
    try:
        import requests

        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code != 200
    except:
        return True


class MockResponse:
    """Mock HTTP response for testing."""

    def __init__(self, json_data: Dict[str, Any], status_code: int = 200):
        self.json_data = json_data
        self.status_code = status_code
        self.text = str(json_data)

    def json(self):
        return self.json_data

    def raise_for_status(self):
        if self.status_code >= 400:
            raise Exception(f"HTTP {self.status_code}")


# Test markers for pytest
pytestmark = pytest.mark.unit
