"""
Unit tests for the DocumentDatabase class.

This module tests document database functionality including
document storage, retrieval, and heuristic context handling.
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_document_record, temp_dir

# Import the module under test
try:
    from document_database import DocumentDatabase, DocumentRecord
except ImportError:
    DocumentDatabase = None  # type: ignore
    DocumentRecord = None  # type: ignore


@pytest.mark.unit
@pytest.mark.database
class TestDocumentDatabase:
    """Test cases for DocumentDatabase class."""

    def test_document_database_import(self):
        """Test that DocumentDatabase can be imported successfully."""
        assert DocumentDatabase is not None, "DocumentDatabase should be importable"
        assert DocumentRecord is not None, "DocumentRecord should be importable"

    def test_database_initialization(self, temp_dir):
        """Test DocumentDatabase initialization."""
        if DocumentDatabase is None:
            pytest.skip("DocumentDatabase not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        assert db is not None
        assert Path(db_path).exists()
        assert hasattr(db, "upsert_document")
        assert hasattr(db, "get_document_by_id")

    def test_document_record_creation(self):
        """Test DocumentRecord creation with required fields."""
        if DocumentRecord is None:
            pytest.skip("DocumentRecord not available")

        # Create test document record
        doc = DocumentRecord(
            id="test-doc-001",
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="123",
            date=datetime.now(),
            filename="test.py",
            filepath="/test/test.py",
            size=1024,
            author="Test Author",
            commit_message="Test commit",
            risk_level="LOW",
        )

        assert doc.id == "test-doc-001"
        assert doc.repository_name == "Test Repository"
        assert doc.risk_level == "LOW"

    def test_document_insert_and_retrieve(self, temp_dir):
        """Test document insertion and retrieval."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        # Create test document
        test_doc = DocumentRecord(
            id="test-insert-001",
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="456",
            date=datetime.now(),
            filename="insert_test.py",
            filepath="/test/insert_test.py",
            size=512,
            author="Insert Test Author",
            commit_message="Insert test commit",
            risk_level="MEDIUM",
        )

        # Insert document
        success = db.upsert_document(test_doc)
        assert success is True

        # Retrieve document
        retrieved = db.get_document_by_id("test-insert-001")
        assert retrieved is not None
        assert retrieved.id == "test-insert-001"
        assert retrieved.repository_name == "Test Repository"
        assert retrieved.risk_level == "MEDIUM"

    def test_document_with_heuristic_context(self, temp_dir):
        """Test document storage with heuristic context."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        # Create heuristic context
        heuristic_context = {
            "indicators": {
                "risk_assessment": "CRITICAL - security vulnerability detected",
                "risk_confidence": "0.95",
                "change_type": "SECURITY_FIX",
                "complexity_score": "8.5",
            },
            "analysis": {
                "file_types": ["python", "config"],
                "patterns_detected": ["authentication", "password_handling"],
                "recommendations": ["security_review", "penetration_testing"],
            },
        }

        # Create test document with heuristic context
        test_doc = DocumentRecord(
            id="test-heuristic-001",
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="789",
            date=datetime.now(),
            filename="security_fix.py",
            filepath="/src/auth/security_fix.py",
            size=2048,
            author="Security Team",
            commit_message="Fix authentication vulnerability",
            risk_level="CRITICAL",
            heuristic_context=heuristic_context,
        )

        # Insert document
        success = db.upsert_document(test_doc)
        assert success is True

        # Retrieve and verify heuristic context
        retrieved = db.get_document_by_id("test-heuristic-001")
        assert retrieved is not None
        assert retrieved.heuristic_context is not None
        assert (
            retrieved.heuristic_context["indicators"]["risk_assessment"]
            == "CRITICAL - security vulnerability detected"
        )
        assert retrieved.heuristic_context["indicators"]["risk_confidence"] == "0.95"
        assert (
            "authentication"
            in retrieved.heuristic_context["analysis"]["patterns_detected"]
        )

    def test_document_update(self, temp_dir):
        """Test document update functionality."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        # Create initial document
        original_doc = DocumentRecord(
            id="test-update-001",
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="100",
            date=datetime.now(),
            filename="update_test.py",
            filepath="/test/update_test.py",
            size=256,
            author="Original Author",
            commit_message="Original commit",
            risk_level="LOW",
        )

        # Insert original document
        success = db.upsert_document(original_doc)
        assert success is True

        # Update document
        updated_doc = DocumentRecord(
            id="test-update-001",  # Same ID for update
            repository_id="test-repo",
            repository_name="Test Repository",
            revision="101",
            date=datetime.now(),
            filename="update_test.py",
            filepath="/test/update_test.py",
            size=512,
            author="Updated Author",
            commit_message="Updated commit",
            risk_level="HIGH",
        )

        # Update document
        success = db.upsert_document(updated_doc)
        assert success is True

        # Retrieve and verify update
        retrieved = db.get_document_by_id("test-update-001")
        assert retrieved is not None
        assert str(retrieved.revision) == "101"  # Handle both string and int types
        assert retrieved.author == "Updated Author"
        assert retrieved.risk_level == "HIGH"
        assert retrieved.size == 512

    def test_get_documents_by_repository(self, temp_dir):
        """Test retrieving documents by repository."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        # Create documents for different repositories
        docs = [
            DocumentRecord(
                id=f"repo1-doc-{i}",
                repository_id="repo-1",
                repository_name="Repository 1",
                revision=str(i),
                date=datetime.now(),
                filename=f"file{i}.py",
                filepath=f"/repo1/file{i}.py",
                size=100 * i,
                author="Repo1 Author",
                commit_message=f"Repo1 commit {i}",
                risk_level="LOW",
            )
            for i in range(1, 4)
        ]

        docs.extend(
            [
                DocumentRecord(
                    id=f"repo2-doc-{i}",
                    repository_id="repo-2",
                    repository_name="Repository 2",
                    revision=str(i),
                    date=datetime.now(),
                    filename=f"file{i}.py",
                    filepath=f"/repo2/file{i}.py",
                    size=200 * i,
                    author="Repo2 Author",
                    commit_message=f"Repo2 commit {i}",
                    risk_level="MEDIUM",
                )
                for i in range(1, 3)
            ]
        )

        # Insert all documents
        for doc in docs:
            success = db.upsert_document(doc)
            assert success is True

        # Test repository-specific retrieval
        if hasattr(db, "get_documents_by_repository"):
            repo1_docs = db.get_documents_by_repository("repo-1")
            repo2_docs = db.get_documents_by_repository("repo-2")

            assert len(repo1_docs) == 3
            assert len(repo2_docs) == 2

            # Verify repository filtering
            for doc in repo1_docs:
                assert doc.repository_id == "repo-1"

            for doc in repo2_docs:
                assert doc.repository_id == "repo-2"

    def test_database_error_handling(self, temp_dir):
        """Test database error handling."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_documents.db"
        db = DocumentDatabase(str(db_path))

        # Test retrieving non-existent document
        result = db.get_document_by_id("non-existent-id")
        assert result is None

        # Test invalid document data handling
        try:
            invalid_doc = DocumentRecord(
                id="",  # Invalid empty ID
                repository_id="test-repo",
                repository_name="Test Repository",
                revision="123",
                date=datetime.now(),
                filename="test.py",
                filepath="/test/test.py",
                size=100,
                author="Test Author",
                commit_message="Test commit",
                risk_level="LOW",
            )

            # Should handle gracefully
            result = db.upsert_document(invalid_doc)
            # Result depends on implementation - could be False or raise exception
            assert isinstance(result, bool)

        except Exception as e:
            # Exception handling is acceptable for invalid data
            assert isinstance(e, (ValueError, sqlite3.Error))


@pytest.mark.integration
@pytest.mark.database
class TestDocumentDatabaseIntegration:
    """Integration tests for DocumentDatabase with realistic scenarios."""

    def test_large_document_batch(self, temp_dir):
        """Test handling of large document batches."""
        if DocumentDatabase is None or DocumentRecord is None:
            pytest.skip("DocumentDatabase or DocumentRecord not available")

        db_path = temp_dir / "test_large_batch.db"
        db = DocumentDatabase(str(db_path))

        # Create batch of documents
        batch_size = 100
        docs = []

        for i in range(batch_size):
            doc = DocumentRecord(
                id=f"batch-doc-{i:03d}",
                repository_id="batch-repo",
                repository_name="Batch Test Repository",
                revision=str(i),
                date=datetime.now(),
                filename=f"batch_file_{i}.py",
                filepath=f"/batch/batch_file_{i}.py",
                size=1024 + i,
                author=f"Batch Author {i % 5}",
                commit_message=f"Batch commit {i}",
                risk_level=["LOW", "MEDIUM", "HIGH", "CRITICAL"][i % 4],
            )
            docs.append(doc)

        # Insert all documents
        success_count = 0
        for doc in docs:
            if db.upsert_document(doc):
                success_count += 1

        # Verify batch insertion
        assert success_count == batch_size

        # Test batch retrieval
        if hasattr(db, "get_documents_by_repository"):
            retrieved_docs = db.get_documents_by_repository("batch-repo")
            assert len(retrieved_docs) == batch_size


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
