# RepoSense AI Unit Tests

This directory contains comprehensive unit tests for the RepoSense AI application using pytest framework.

## 🚀 Quick Start

### Running Tests in Visual Studio Code

1. **Open the project** in VS Code
2. **Install Python extension** if not already installed
3. **Open Command Palette** (`Ctrl+Shift+P`)
4. **Run "Python: Configure Tests"** and select pytest
5. **Click the test beaker icon** in the sidebar to see all tests
6. **Run tests** by clicking the play button next to individual tests or test classes

### Running Tests from Command Line

```bash
# Install testing dependencies
pip install -r requirements.txt

# Run all tests
pytest reposense_ai/unittests/

# Run with verbose output
pytest reposense_ai/unittests/ -v

# Run specific test file
pytest reposense_ai/unittests/test_config_manager.py

# Run specific test function
pytest reposense_ai/unittests/test_config_manager.py::test_basic_functionality

# Run tests with coverage
pytest reposense_ai/unittests/ --cov=reposense_ai --cov-report=html
```

### Windows Batch File (Easy Option)

For Windows users, use the convenient batch file:

```cmd
# Navigate to unittests directory
cd reposense_ai/unittests

# Run all tests
run_tests.bat

# Run specific test categories
run_tests.bat --basic
run_tests.bat --unit
run_tests.bat --coverage
run_tests.bat --config
```

## 📁 Directory Structure

```
unittests/
├── __init__.py                    # Package initialization
├── README.md                      # This file
├── run_tests.bat                  # Windows test runner script
├── test_utils.py                  # Common test utilities and fixtures
├── test_config_manager.py         # ConfigManager tests
├── test_ollama_client.py          # OllamaClient and AI service tests
├── test_email_service.py          # EmailService and notification tests
├── test_document_database.py      # DocumentDatabase and storage tests
├── test_svn_backend.py            # SVN backend and repository tests
├── test_monitor_service.py        # MonitorService and workflow tests
└── [additional test files]        # Future test modules
```

## 🏷️ Test Categories

Tests are organized using pytest markers:

- `@pytest.mark.unit` - Unit tests for individual components
- `@pytest.mark.integration` - Integration tests for component interactions
- `@pytest.mark.slow` - Tests that take longer to run
- `@pytest.mark.database` - Tests requiring database access
- `@pytest.mark.network` - Tests requiring network access
- `@pytest.mark.ai` - Tests requiring AI/LLM services

### Running Specific Categories

```bash
# Run only unit tests
pytest reposense_ai/unittests/ -m unit

# Run only integration tests
pytest reposense_ai/unittests/ -m integration

# Skip slow tests
pytest reposense_ai/unittests/ -m "not slow"

# Run database tests only
pytest reposense_ai/unittests/ -m database
```

## 🛠️ Test Utilities

The `test_utils.py` module provides:

### Fixtures
- `temp_dir` - Temporary directory for test files
- `mock_config` - Mock configuration object
- `mock_document_record` - Mock document record
- `mock_ollama_client` - Mock AI client

### Helper Functions
- `create_test_file()` - Create test files with content
- `create_mock_database()` - Create mock database
- `assert_file_exists()` - Assert file existence
- `assert_file_contains()` - Assert file content
- `skip_if_no_network()` - Skip tests without network
- `skip_if_no_ai_service()` - Skip tests without AI service

### Example Usage

```python
import pytest
from test_utils import temp_dir, mock_config, create_test_file

@pytest.mark.unit
def test_example(temp_dir, mock_config):
    # Create a test file
    test_file = create_test_file(temp_dir, "test.txt", "test content")
    
    # Use mock config
    assert mock_config.ollama_base_url == "http://localhost:11434"
    
    # Test your functionality
    assert test_file.exists()
```

## 🎯 Writing New Tests

### Test File Naming
- Test files should start with `test_`
- Name after the module being tested: `test_module_name.py`

### Test Function Naming
- Test functions should start with `test_`
- Use descriptive names: `test_config_loading_with_valid_file`

### Test Class Organization
```python
@pytest.mark.unit
class TestModuleName:
    """Test cases for ModuleName class."""
    
    def test_functionality_description(self):
        """Test specific functionality with clear description."""
        # Arrange
        # Act
        # Assert
```

### Best Practices
1. **Use descriptive test names** that explain what is being tested
2. **Follow AAA pattern** (Arrange, Act, Assert)
3. **Use appropriate markers** to categorize tests
4. **Mock external dependencies** (databases, network calls, AI services)
5. **Test both success and failure cases**
6. **Keep tests independent** - each test should be able to run alone

## 🔧 VS Code Integration

The project is configured with:

### Test Discovery
- Automatic test discovery in `reposense_ai/unittests/`
- Test explorer in sidebar shows all tests
- Run/debug individual tests or entire suites

### Debugging
- Set breakpoints in test code
- Use "Python: Debug Current Test" launch configuration
- Step through test execution with full debugging support

### Coverage
- Install `pytest-cov` for coverage reporting
- Generate HTML coverage reports
- View coverage in VS Code with Coverage Gutters extension

## 📊 Coverage Reporting

```bash
# Generate HTML coverage report
pytest reposense_ai/unittests/ --cov=reposense_ai --cov-report=html

# View coverage report
# Open htmlcov/index.html in browser

# Generate terminal coverage report
pytest reposense_ai/unittests/ --cov=reposense_ai --cov-report=term-missing
```

## 🚨 Troubleshooting

### VS Code "Test loading failed" Error
**Problem**: `Error: spawn ./venv/Scripts/python.exe ENOENT`
**Solution**:
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type `Python: Select Interpreter`
3. Choose your system Python installation (not a non-existent virtual environment)

### Import Errors
- Ensure `PYTHONPATH` includes the `reposense_ai` directory
- Check that `__init__.py` files exist in test directories
- Verify VS Code Python interpreter is set correctly

### Test Discovery Issues
- Check pytest configuration in `pytest.ini`
- Verify test file naming conventions
- Ensure test functions start with `test_`
- Refresh test discovery in VS Code Test Explorer

### Mock/Fixture Issues
- Import fixtures from `test_utils`
- Check fixture scope (function, class, module, session)
- Verify mock objects have required attributes

### Python Extension Issues
- Reload VS Code window: `Ctrl+Shift+P` → `Developer: Reload Window`
- Restart VS Code completely
- Check Python extension is enabled and up to date

## 📋 Test Module Descriptions

### Core Component Tests
- **`test_config_manager.py`** - Configuration loading, validation, and management
- **`test_ollama_client.py`** - AI service integration, model selection, and content generation
- **`test_email_service.py`** - Email notifications, SMTP configuration, and recipient management
- **`test_document_database.py`** - Document storage, retrieval, and heuristic context handling
- **`test_monitor_service.py`** - Repository monitoring, workflow integration, and service coordination

### Backend Integration Tests
- **`test_svn_backend.py`** - SVN repository integration, authentication, and branch discovery

### Test Categories by Functionality
- **Configuration & Setup**: `test_config_manager.py`
- **AI & Machine Learning**: `test_ollama_client.py`
- **Communication**: `test_email_service.py`
- **Data Storage**: `test_document_database.py`
- **Repository Integration**: `test_svn_backend.py`
- **Workflow & Orchestration**: `test_monitor_service.py`

## 📈 Future Enhancements

Planned additions to the test framework:
- Git backend integration tests
- Web interface endpoint tests
- Document processing pipeline tests
- Performance/load tests
- End-to-end workflow tests
- Automated test reporting
- CI/CD integration
